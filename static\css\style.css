/* ATE MEG's FROZEN FOODS - Custom Styles */

:root {
    /* Enhanced Color Palette */
    --primary-color: #4f46e5;
    --primary-light: #6366f1;
    --primary-dark: #3730a3;
    --primary-subtle: #e0e7ff;

    --secondary-color: #6b7280;
    --secondary-light: #9ca3af;
    --secondary-dark: #374151;
    --secondary-subtle: #f3f4f6;

    --success-color: #059669;
    --success-light: #10b981;
    --success-dark: #047857;
    --success-subtle: #d1fae5;

    --danger-color: #dc2626;
    --danger-light: #ef4444;
    --danger-dark: #b91c1c;
    --danger-subtle: #fee2e2;

    --warning-color: #d97706;
    --warning-light: #f59e0b;
    --warning-dark: #b45309;
    --warning-subtle: #fef3c7;

    --info-color: #0891b2;
    --info-light: #06b6d4;
    --info-dark: #0e7490;
    --info-subtle: #cffafe;

    --light-color: #f8fafc;
    --light-subtle: #f1f5f9;
    --dark-color: #0f172a;
    --dark-subtle: #1e293b;

    --text-light: #ffffff;
    --text-dark: #1e293b;
    --text-muted: #64748b;
    --text-subtle: #94a3b8;

    /* Enhanced Spacing System */
    --spacing-xs: 0.25rem;    /* 4px */
    --spacing-sm: 0.5rem;     /* 8px */
    --spacing-md: 0.75rem;    /* 12px */
    --spacing-lg: 1rem;       /* 16px */
    --spacing-xl: 1.5rem;     /* 24px */
    --spacing-2xl: 2rem;      /* 32px */
    --spacing-3xl: 2.5rem;    /* 40px */
    --spacing-4xl: 3rem;      /* 48px */

    /* Enhanced Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-hover: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Enhanced Border Radius */
    --border-radius-sm: 0.375rem;
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-2xl: 1.5rem;

    /* Enhanced Gradients */
    --gradient-primary: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
    --gradient-secondary: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
    --gradient-success: linear-gradient(135deg, #059669 0%, #10b981 100%);
    --gradient-warning: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
    --gradient-danger: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
    --gradient-info: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);

    /* Subtle Background Gradients */
    --gradient-bg-light: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
    --gradient-bg-subtle: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.9) 100%);
}

/* Global Styles */
body {
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    background: var(--gradient-bg-light);
    min-height: 100vh;
    color: var(--text-dark);
    line-height: 1.6;
    font-weight: 400;
    padding: 0;
    margin: 0;
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    letter-spacing: -0.025em;
    color: var(--text-dark);
    margin-bottom: var(--spacing-lg);
}

h1 {
    font-size: 2.25rem;
    margin-bottom: var(--spacing-xl);
}
h2 {
    font-size: 1.875rem;
    margin-bottom: var(--spacing-lg);
}
h3 {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-md);
}
h4 {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-md);
}
h5 {
    font-size: 1.125rem;
    margin-bottom: var(--spacing-sm);
}

/* Enhanced Container with Better Spacing */
.container-fluid {
    max-width: 1400px;
    margin: 0 auto;
    padding-left: var(--spacing-lg);
    padding-right: var(--spacing-lg);
}

/* Main Content Area with Enhanced Spacing */
.main-content {
    padding: var(--spacing-xl) 0;
    min-height: calc(100vh - 200px);
}

/* Row Spacing Enhancement */
.row {
    margin-left: calc(-1 * var(--spacing-md));
    margin-right: calc(-1 * var(--spacing-md));
}

.row > * {
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
}

/* Glass Effect */
.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Modern Navigation */
.navbar {
    background: var(--gradient-primary) !important;
    box-shadow: var(--shadow-lg);
    padding: 1rem 0;
    min-height: 80px;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
    color: var(--text-light) !important;
    text-decoration: none;
    display: flex;
    align-items: center;
}

.navbar-brand .brand-text {
    font-size: 1.2rem;
    margin-left: 0.5rem;
}

.navbar-brand .trademark {
    font-size: 0.7rem;
    vertical-align: super;
    opacity: 0.8;
}

/* Mobile Navigation Improvements */
.navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
    font-size: 1.25rem;
}

.navbar-toggler:focus {
    box-shadow: none;
}

.nav-link {
    color: var(--text-light) !important;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: var(--border-radius);
    margin: 0 0.2rem;
    padding: 0.5rem 1rem !important;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Enhanced Cards with Better Spacing */
.card {
    border: none;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    overflow: hidden;
    margin-bottom: var(--spacing-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-6px) scale(1.01);
    border-color: var(--primary-subtle);
}

.card-header {
    background: var(--gradient-bg-subtle);
    border-bottom: 2px solid var(--primary-subtle);
    border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0 !important;
    padding: var(--spacing-xl) var(--spacing-2xl);
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0;
}

.card-body {
    padding: var(--spacing-2xl);
}

.card-footer {
    background: var(--light-subtle);
    border-top: 1px solid var(--secondary-subtle);
    padding: var(--spacing-lg) var(--spacing-2xl);
}

/* Enhanced Gradient Cards with Better Spacing */
.card-gradient-primary {
    background: var(--gradient-primary);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
    margin-bottom: var(--spacing-xl);
}

.card-gradient-primary .card-body {
    padding: var(--spacing-2xl) var(--spacing-xl);
}

.card-gradient-success {
    background: var(--gradient-success);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
    margin-bottom: var(--spacing-xl);
}

.card-gradient-success .card-body {
    padding: var(--spacing-2xl) var(--spacing-xl);
}

.card-gradient-warning {
    background: var(--gradient-warning);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
    margin-bottom: var(--spacing-xl);
}

.card-gradient-warning .card-body {
    padding: var(--spacing-2xl) var(--spacing-xl);
}

.card-gradient-danger {
    background: var(--gradient-danger);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
    margin-bottom: var(--spacing-xl);
}

.card-gradient-danger .card-body {
    padding: var(--spacing-2xl) var(--spacing-xl);
}

.card-gradient-info {
    background: var(--gradient-info);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
    margin-bottom: var(--spacing-xl);
}

.card-gradient-info .card-body {
    padding: var(--spacing-2xl) var(--spacing-xl);
}

.card-gradient-secondary {
    background: var(--gradient-secondary);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
    margin-bottom: var(--spacing-xl);
}

.card-gradient-secondary .card-body {
    padding: var(--spacing-2xl) var(--spacing-xl);
}

/* Enhanced Buttons with Better Spacing */
.btn {
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    position: relative;
    overflow: hidden;
    text-transform: none;
    letter-spacing: 0.025em;
    margin: var(--spacing-xs) var(--spacing-sm);
    box-shadow: var(--shadow-sm);
}

.btn:first-child {
    margin-left: 0;
}

.btn:last-child {
    margin-right: 0;
}

.btn-lg {
    padding: var(--spacing-lg) var(--spacing-2xl);
    font-size: 1.125rem;
    margin: var(--spacing-sm) var(--spacing-md);
}

.btn-sm {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: 0.875rem;
    margin: var(--spacing-xs);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-success {
    background: var(--gradient-success);
    box-shadow: var(--shadow);
}

.btn-success:hover {
    background: #059669;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-danger {
    background: var(--gradient-danger);
    box-shadow: var(--shadow);
}

.btn-danger:hover {
    background: #dc2626;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-warning {
    background: var(--gradient-warning);
    color: var(--text-dark);
    box-shadow: var(--shadow);
}

.btn-warning:hover {
    background: #d97706;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-info {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    box-shadow: var(--shadow);
}

.btn-info:hover {
    background: #0e7490;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Enhanced Dashboard Cards with Better Spacing */
.dashboard-card {
    background: rgba(255, 255, 255, 0.98);
    color: var(--text-dark);
    border: 2px solid var(--secondary-subtle);
    border-radius: var(--border-radius-2xl);
    padding: var(--spacing-3xl) var(--spacing-2xl);
    text-align: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(15px);
    margin-bottom: var(--spacing-xl);
    position: relative;
    overflow: hidden;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.dashboard-card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-8px) scale(1.03);
    border-color: var(--primary-light);
}

/* Enhanced Tables with Better Spacing */
.table {
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    background: white;
    margin-bottom: var(--spacing-2xl);
    border: 2px solid var(--secondary-subtle);
}

.table thead th {
    background: var(--gradient-primary);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.875rem;
    padding: var(--spacing-lg) var(--spacing-xl);
    position: relative;
}

.table thead th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: rgba(255, 255, 255, 0.3);
}

.table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid var(--secondary-subtle);
}

.table tbody tr:hover {
    background-color: var(--primary-subtle);
    transform: scale(1.005);
    box-shadow: inset 0 0 0 2px var(--primary-light);
}

.table td {
    padding: var(--spacing-lg) var(--spacing-xl);
    border-color: var(--secondary-subtle);
    vertical-align: middle;
    font-weight: 500;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

.dashboard-card h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.dashboard-card .card-footer {
    background: var(--light-color);
    border-top: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.dashboard-card .card-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

/* Enhanced Product Cards with Better Spacing */
.product-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: var(--border-radius-xl);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border: 2px solid var(--secondary-subtle);
    margin-bottom: var(--spacing-xl);
    overflow: hidden;
    position: relative;
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-info);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover::before {
    opacity: 1;
}

.product-card:hover {
    transform: translateY(-10px) scale(1.03);
    box-shadow: var(--shadow-hover);
    background: rgba(255, 255, 255, 1);
    border-color: var(--info-light);
}

.product-card .card-body {
    padding: var(--spacing-2xl);
    text-align: center;
}

.product-card .btn {
    width: 100%;
    margin-top: var(--spacing-lg);
    margin-left: 0;
    margin-right: 0;
}

.product-card .quantity-controls {
    margin: var(--spacing-lg) 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.product-card .quantity-controls .input-group {
    justify-content: center;
}

.product-card .quick-qty {
    margin-top: var(--spacing-sm);
}

.product-card .quick-qty .btn {
    margin: var(--spacing-xs);
    width: auto;
}

/* Enhanced POS Styles with Better Spacing */
.pos-container {
    background: rgba(255, 255, 255, 0.98);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-lg);
    border: 2px solid var(--secondary-subtle);
    backdrop-filter: blur(15px);
    margin-bottom: var(--spacing-xl);
}

.cart-item {
    background: var(--light-subtle);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    border: 2px solid var(--secondary-subtle);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.cart-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--gradient-success);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.cart-item:hover::before {
    opacity: 1;
}

.cart-item:hover {
    border-color: var(--success-light);
    box-shadow: var(--shadow-md);
    background: white;
    transform: translateX(4px);
}

.cart-total {
    background: var(--gradient-primary);
    color: var(--text-light);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-xl);
    text-align: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin: var(--spacing-xl) 0;
    box-shadow: var(--shadow-lg);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

/* Tables */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.table thead th {
    background: var(--dark-color);
    color: var(--text-light);
    border: none;
    font-weight: 600;
    padding: 1rem;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background: rgba(0, 0, 0, 0.05);
}

/* Status Badges */
.badge {
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    font-weight: 500;
}

.low-stock {
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid var(--warning-color);
}

.out-of-stock {
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid var(--danger-color);
}

/* Enhanced Forms with Better Spacing */
.form-control {
    border-radius: var(--border-radius-lg);
    border: 2px solid var(--secondary-subtle);
    padding: var(--spacing-md) var(--spacing-lg);
    transition: all 0.3s ease;
    font-weight: 500;
    margin-bottom: var(--spacing-md);
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem var(--primary-subtle);
    background: white;
    transform: scale(1.01);
}

.form-label {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: var(--spacing-sm);
}

.form-group {
    margin-bottom: var(--spacing-xl);
}

/* Enhanced Alerts with Better Spacing */
.alert {
    border-radius: var(--border-radius-xl);
    border: 2px solid transparent;
    padding: var(--spacing-lg) var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 5px;
}

.alert-success {
    background: var(--success-subtle);
    border-color: var(--success-color);
    color: var(--success-dark);
}

.alert-success::before {
    background: var(--success-color);
}

.alert-warning {
    background: var(--warning-subtle);
    border-color: var(--warning-color);
    color: var(--warning-dark);
}

.alert-warning::before {
    background: var(--warning-color);
}

.alert-danger {
    background: var(--danger-subtle);
    border-color: var(--danger-color);
    color: var(--danger-dark);
}

.alert-danger::before {
    background: var(--danger-color);
}

.alert-info {
    background: var(--info-subtle);
    border-color: var(--info-color);
    color: var(--info-dark);
}

.alert-info::before {
    background: var(--info-color);
}

/* Enhanced List Styles */
.list-group-item {
    border: 2px solid var(--secondary-subtle);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-lg);
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.list-group-item:hover {
    border-color: var(--primary-light);
    background: white;
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
}

.list-group-item:last-child {
    margin-bottom: 0;
}

/* Enhanced Spacing Utilities */
.mb-custom {
    margin-bottom: var(--spacing-xl) !important;
}

.mt-custom {
    margin-top: var(--spacing-xl) !important;
}

.p-custom {
    padding: var(--spacing-xl) !important;
}

.gap-custom {
    gap: var(--spacing-lg) !important;
}

/* Enhanced Footer */
.footer {
    background: var(--dark-color);
    color: var(--text-light);
    padding: var(--spacing-4xl) 0 var(--spacing-2xl);
    margin-top: var(--spacing-4xl);
    border-top: 4px solid var(--primary-color);
}

.footer a {
    color: var(--text-light);
    text-decoration: none;
    transition: all 0.3s ease;
    padding: var(--spacing-xs) 0;
    display: inline-block;
}

.footer a:hover {
    color: var(--info-color);
    text-decoration: underline;
    transform: translateX(4px);
}

/* Mobile Product Styles */
.mobile-products-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    padding: var(--spacing-sm);
}

.mobile-product-item {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.mobile-product-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.mobile-product-info {
    flex: 1;
    margin-right: var(--spacing-md);
}

.mobile-product-name {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 1rem;
    margin-bottom: var(--spacing-xs);
}

.mobile-product-price {
    color: var(--success-color);
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: var(--spacing-xs);
}

.mobile-product-stock {
    color: var(--text-muted);
    font-size: 0.85rem;
}

.mobile-product-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
}

.mobile-qty-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-xs);
}

.mobile-qty-input {
    width: 50px;
    text-align: center;
}

.mobile-add-btn {
    min-width: 40px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Enhanced Mobile Responsiveness with Better Spacing */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
        flex-grow: 1;
    }

    .navbar-brand .brand-text {
        font-size: 1rem;
    }

    /* Improve navbar layout on tablets */
    .navbar-toggler {
        margin-left: auto;
    }

    /* Mobile Navigation Menu Improvements */
    .navbar-collapse {
        background: var(--primary-color);
        border-radius: var(--border-radius-lg);
        margin-top: var(--spacing-md);
        padding: var(--spacing-lg);
        box-shadow: var(--shadow-lg);
    }

    /* Enhanced Mobile Spacing */
    .container-fluid {
        padding-left: var(--spacing-md);
        padding-right: var(--spacing-md);
    }

    .main-content {
        padding: var(--spacing-lg) 0;
    }

    .card {
        margin-bottom: var(--spacing-lg);
    }

    .card-body {
        padding: var(--spacing-lg);
    }

    .card-header {
        padding: var(--spacing-lg);
    }

    .row > * {
        padding-left: var(--spacing-sm);
        padding-right: var(--spacing-sm);
    }

    .nav-link {
        padding: 1rem 1.5rem !important;
        margin: 0.2rem 0;
        border-radius: var(--border-radius);
        font-size: 1.1rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .nav-link:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateX(5px);
    }

    .nav-link i {
        margin-right: 0.75rem;
        font-size: 1.2rem;
        width: 20px;
        text-align: center;
    }

    .nav-link span {
        font-size: 1rem;
        font-weight: 600;
        color: var(--text-light);
    }
    
    .dashboard-card h3 {
        font-size: 2rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .table-responsive {
        font-size: 0.85rem;
    }
    
    .pos-container {
        padding: 1rem;
    }
    
    .cart-total {
        font-size: 1.2rem;
    }
}

@media (max-width: 576px) {
    /* Keep brand text visible but make it smaller */
    .navbar-brand {
        font-size: 1rem;
        flex-grow: 1;
    }

    .navbar-brand .brand-text {
        font-size: 0.85rem;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
    }

    /* Ensure hamburger menu doesn't overlap */
    .navbar-toggler {
        margin-left: auto;
        border: none;
        padding: 0.25rem 0.5rem;
    }

    /* Enhanced Mobile Navigation */
    .navbar-collapse {
        margin-top: 0.75rem;
        padding: 0.75rem;
    }

    .nav-link {
        padding: 0.875rem 1.25rem !important;
        font-size: 1rem;
        margin: 0.15rem 0;
    }

    .nav-link i {
        margin-right: 0.5rem;
        font-size: 1.1rem;
        width: 18px;
    }

    .nav-link span {
        font-size: 0.95rem;
        font-weight: 600;
    }

    /* Make container more mobile-friendly */
    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .dashboard-card {
        padding: 1.5rem;
    }

    .dashboard-card h3 {
        font-size: 1.8rem;
    }

    .product-card .card-body {
        padding: 1rem;
    }

    .btn-group .btn {
        padding: 0.3rem 0.6rem;
        font-size: 0.8rem;
    }
}

/* Extra small screens (phones < 400px) */
@media (max-width: 400px) {
    .navbar-brand .brand-text {
        font-size: 0.75rem;
        max-width: 150px;
    }

    .navbar-brand i {
        font-size: 1rem;
    }

    .navbar-toggler {
        padding: 0.2rem 0.4rem;
        font-size: 1rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Creator Logo Styles */
.creator-logo {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 12px;
    border: 3px solid rgba(255, 255, 255, 0.4);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.creator-logo:hover {
    border-color: rgba(255, 255, 255, 0.9);
    transform: scale(1.5);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    position: relative;
}

.creator-logo-small {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.creator-logo-small:hover {
    border-color: rgba(255, 255, 255, 0.8);
    transform: scale(1.8);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
    z-index: 1000;
    position: relative;
}

/* Ensure navbar brand has enough space for hover effect */
.navbar-brand {
    position: relative;
    z-index: 1;
}

/* Ensure footer has enough space for hover effect */
.footer h5 {
    position: relative;
    z-index: 1;
}

/* Enhanced Spacing Utility Classes */
.spacing-xs { margin: var(--spacing-xs) !important; }
.spacing-sm { margin: var(--spacing-sm) !important; }
.spacing-md { margin: var(--spacing-md) !important; }
.spacing-lg { margin: var(--spacing-lg) !important; }
.spacing-xl { margin: var(--spacing-xl) !important; }
.spacing-2xl { margin: var(--spacing-2xl) !important; }

.p-xs { padding: var(--spacing-xs) !important; }
.p-sm { padding: var(--spacing-sm) !important; }
.p-md { padding: var(--spacing-md) !important; }
.p-lg { padding: var(--spacing-lg) !important; }
.p-xl { padding: var(--spacing-xl) !important; }
.p-2xl { padding: var(--spacing-2xl) !important; }

.mb-xs { margin-bottom: var(--spacing-xs) !important; }
.mb-sm { margin-bottom: var(--spacing-sm) !important; }
.mb-md { margin-bottom: var(--spacing-md) !important; }
.mb-lg { margin-bottom: var(--spacing-lg) !important; }
.mb-xl { margin-bottom: var(--spacing-xl) !important; }
.mb-2xl { margin-bottom: var(--spacing-2xl) !important; }

.mt-xs { margin-top: var(--spacing-xs) !important; }
.mt-sm { margin-top: var(--spacing-sm) !important; }
.mt-md { margin-top: var(--spacing-md) !important; }
.mt-lg { margin-top: var(--spacing-lg) !important; }
.mt-xl { margin-top: var(--spacing-xl) !important; }
.mt-2xl { margin-top: var(--spacing-2xl) !important; }

/* Enhanced Button Groups */
.btn-group .btn {
    margin: 0 var(--spacing-xs);
}

.btn-group .btn:first-child {
    margin-left: 0;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* Enhanced Grid Spacing */
.row.g-custom {
    --bs-gutter-x: var(--spacing-xl);
    --bs-gutter-y: var(--spacing-xl);
}

.row.g-custom-lg {
    --bs-gutter-x: var(--spacing-2xl);
    --bs-gutter-y: var(--spacing-2xl);
}

/* Enhanced Custom Scrollbar */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: var(--light-subtle);
    border-radius: var(--border-radius-lg);
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: var(--border-radius-lg);
    border: 2px solid var(--light-subtle);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gradient-secondary);
}

/* Table Alignment Improvements */
.table th,
.table td {
    vertical-align: middle;
    white-space: nowrap;
}

.table .text-nowrap {
    white-space: nowrap !important;
}

.table .btn-group,
.table .d-flex {
    justify-content: center;
    align-items: center;
}

.table .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.2;
}

/* Navigation Improvements */
.navbar-nav .nav-link {
    display: flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    white-space: nowrap;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link i {
    flex-shrink: 0;
}

/* Navigation text control */
.nav-text {
    display: inline;
}

/* Hide navigation text on smaller screens when navbar is collapsed */
@media (max-width: 991px) {
    .navbar-collapse:not(.show) .nav-text {
        display: none !important;
    }

    .navbar-collapse:not(.show) .navbar-nav .nav-link i {
        margin-right: 0 !important;
    }

    .navbar-collapse:not(.show) .navbar-nav .nav-link {
        padding: 0.5rem 0.5rem;
    }
}

/* Ensure text is visible in expanded mobile menu */
.navbar-collapse.show .nav-text {
    display: inline !important;
}

/* Override responsive classes when menu is expanded */
@media (max-width: 991px) {
    .navbar-collapse.show .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }

    .navbar-collapse.show .navbar-nav .nav-link:hover {
        background-color: rgba(255,255,255,0.1);
    }

    .navbar-collapse.show .navbar-nav .nav-link i {
        margin-right: 0.5rem !important;
    }
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
    .navbar-brand .brand-text {
        font-size: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        font-size: 0.875rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .table .btn-sm {
        padding: 0.2rem 0.4rem;
        font-size: 0.7rem;
    }

    .modal-dialog {
        margin: 0.5rem;
    }

    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
}

/* Extra small screens - enhanced mobile navigation */
@media (max-width: 576px) {
    /* When navbar is collapsed, hide text and show only icons */
    .navbar-collapse:not(.show) .nav-text {
        display: none !important;
    }

    .navbar-collapse:not(.show) .navbar-nav .nav-link i {
        margin-right: 0 !important;
    }

    .navbar-collapse:not(.show) .navbar-nav .nav-link {
        padding: 0.4rem 0.4rem;
        min-width: 44px; /* Ensure touch-friendly size */
        justify-content: center;
    }

    /* When navbar is expanded, show text with better spacing */
    .navbar-collapse.show .nav-text {
        display: inline !important;
        margin-left: 0.5rem;
    }

    .navbar-collapse.show .navbar-nav .nav-link {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid rgba(255,255,255,0.1);
        font-size: 1.1rem;
    }

    .navbar-collapse.show .navbar-nav .nav-link i {
        margin-right: 0.75rem !important;
        width: 20px;
        text-align: center;
    }
}
